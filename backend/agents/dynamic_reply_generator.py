# -*- coding: utf-8 -*-
"""
动态LLM回复生成器 - 统一管理所有动态LLM回复生成逻辑
版本: v1.0
作者: AI Assistant
创建时间: 2025-06-20

功能:
1. 统一LLM调用逻辑，避免重复代码
2. 提供统一的错误处理和回退机制
3. 支持多种提示词构建策略
4. 提供响应质量验证和清理
5. 支持性能监控和缓存
"""

import logging
import asyncio
import time
from typing import Dict, Any, Optional, Callable
from enum import Enum
from dataclasses import dataclass
from backend.utils.prompt_loader import PromptLoader


class PromptStrategy(Enum):
    """提示词构建策略"""
    SIMPLE = "simple"                    # 简单策略：直接使用prompt_instruction
    WITH_USER_INPUT = "with_user_input"  # 包含用户输入：prompt_instruction + 用户输入
    TEMPLATE_BASED = "template_based"    # 基于模板：使用PromptLoader加载模板
    CUSTOM = "custom"                    # 自定义策略：使用自定义构建函数


class ResponseQuality(Enum):
    """响应质量等级"""
    EXCELLENT = "excellent"  # 优秀：长度适中，内容丰富
    GOOD = "good"           # 良好：基本符合要求
    ACCEPTABLE = "acceptable"  # 可接受：有一定问题但可用
    POOR = "poor"           # 较差：质量不佳，需要回退


@dataclass
class LLMCallConfig:
    """LLM调用配置"""
    agent_name: str = "default_generator"
    temperature: float = 0.7
    max_tokens: int = 200
    timeout: int = 10
    max_retries: int = 2
    enable_cache: bool = False
    cache_ttl: int = 300  # 缓存时间（秒）


@dataclass
class GenerationContext:
    """生成上下文"""
    prompt_instruction: str
    user_message: str = ""
    session_id: str = ""
    additional_context: Dict[str, Any] = None
    template_name: str = ""
    template_variables: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_context is None:
            self.additional_context = {}
        if self.template_variables is None:
            self.template_variables = {}


class ResponseValidator:
    """响应质量验证器"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def validate_response(self, response: str, context: GenerationContext = None) -> ResponseQuality:
        """验证响应质量"""
        if not response or not response.strip():
            return ResponseQuality.POOR
        
        response = response.strip()
        
        # 基本质量检查
        if len(response) < 10:
            return ResponseQuality.POOR
        
        # 检查是否包含明显的错误标识
        error_indicators = [
            "抱歉，我无法", "系统错误", "处理失败", "出现问题",
            "Error", "Exception", "Failed", "null", "undefined"
        ]
        
        for indicator in error_indicators:
            if indicator.lower() in response.lower():
                return ResponseQuality.POOR
        
        # 长度和内容质量评估
        if len(response) > 500:
            return ResponseQuality.EXCELLENT
        elif len(response) > 100:
            return ResponseQuality.GOOD
        elif len(response) > 30:
            return ResponseQuality.ACCEPTABLE
        else:
            return ResponseQuality.POOR
    
    def clean_response(self, response: str) -> str:
        """清理响应内容"""
        if not response:
            return ""
        
        response = response.strip()
        
        # 移除常见的前缀
        prefixes_to_remove = [
            "以下是回复：", "回复：", "我的回复是：", "生成的回复：",
            "建议回复：", "以下是我生成的回复：", "回答：", "答案：",
            "AI回复：", "助手回复：", "系统回复："
        ]
        
        for prefix in prefixes_to_remove:
            if response.startswith(prefix):
                response = response[len(prefix):].strip()
        
        # 移除多余的引号
        if response.startswith('"') and response.endswith('"'):
            response = response[1:-1].strip()
        if response.startswith("'") and response.endswith("'"):
            response = response[1:-1].strip()
        
        return response


class PromptBuilder:
    """提示词构建器"""
    
    def __init__(self, prompt_loader: PromptLoader, logger):
        self.prompt_loader = prompt_loader
        self.logger = logger
    
    def build_prompt(self, strategy: PromptStrategy, context: GenerationContext, custom_builder: Callable = None) -> str:
        """根据策略构建提示词"""
        try:
            if strategy == PromptStrategy.SIMPLE:
                return self._build_simple_prompt(context)
            elif strategy == PromptStrategy.WITH_USER_INPUT:
                return self._build_with_user_input_prompt(context)
            elif strategy == PromptStrategy.TEMPLATE_BASED:
                return self._build_template_based_prompt(context)
            elif strategy == PromptStrategy.CUSTOM and custom_builder:
                return custom_builder(context)
            else:
                self.logger.warning(f"未知的提示词策略: {strategy}, 使用简单策略")
                return self._build_simple_prompt(context)
        except Exception as e:
            self.logger.error(f"构建提示词失败: {e}")
            return context.prompt_instruction
    
    def _build_simple_prompt(self, context: GenerationContext) -> str:
        """构建简单提示词"""
        return context.prompt_instruction
    
    def _build_with_user_input_prompt(self, context: GenerationContext) -> str:
        """构建包含用户输入的提示词"""
        if context.user_message:
            return f"{context.prompt_instruction}\n\n用户输入: {context.user_message}"
        return context.prompt_instruction
    
    def _build_template_based_prompt(self, context: GenerationContext) -> str:
        """构建基于模板的提示词"""
        if not context.template_name:
            self.logger.warning("模板名称为空，使用简单策略")
            return self._build_simple_prompt(context)
        
        try:
            # 准备模板变量
            template_vars = {
                "user_input": context.user_message,
                "prompt_instruction": context.prompt_instruction,
                "session_id": context.session_id,
                **context.template_variables,
                **context.additional_context
            }
            
            return self.prompt_loader.load_prompt(context.template_name, template_vars)
        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")
            return self._build_with_user_input_prompt(context)


class DynamicReplyGenerator:
    """动态LLM回复生成器"""
    
    def __init__(self, llm_client=None):
        """
        初始化动态回复生成器
        
        Args:
            llm_client: LLM客户端实例
        """
        self.logger = logging.getLogger(__name__)
        self.llm_client = llm_client
        self.prompt_loader = PromptLoader()
        self.prompt_builder = PromptBuilder(self.prompt_loader, self.logger)
        self.response_validator = ResponseValidator(self.logger)
        
        # 性能统计
        self.stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "cache_hits": 0,
            "average_response_time": 0.0,
            "quality_distribution": {
                "excellent": 0,
                "good": 0,
                "acceptable": 0,
                "poor": 0
            }
        }
        
        # 简单缓存（生产环境建议使用Redis）
        self._cache = {}
        
        self.logger.info("动态LLM回复生成器初始化完成")
    
    async def generate_reply(
        self,
        context: GenerationContext,
        config: LLMCallConfig = None,
        strategy: PromptStrategy = PromptStrategy.WITH_USER_INPUT,
        fallback_message: str = None,
        custom_prompt_builder: Callable = None
    ) -> str:
        """
        生成动态回复
        
        Args:
            context: 生成上下文
            config: LLM调用配置
            strategy: 提示词构建策略
            fallback_message: 回退消息
            custom_prompt_builder: 自定义提示词构建函数
            
        Returns:
            str: 生成的回复
        """
        start_time = time.time()
        self.stats["total_calls"] += 1
        
        if config is None:
            config = LLMCallConfig()
        
        try:
            # 检查LLM客户端
            if not self.llm_client:
                self.logger.warning("LLM客户端未初始化，返回回退消息")
                return fallback_message or "抱歉，暂时无法生成个性化回复。"
            
            # 构建提示词
            prompt = self.prompt_builder.build_prompt(strategy, context, custom_prompt_builder)
            
            # 检查缓存
            if config.enable_cache:
                cache_key = self._generate_cache_key(prompt, config)
                cached_response = self._get_from_cache(cache_key)
                if cached_response:
                    self.stats["cache_hits"] += 1
                    self.logger.debug("使用缓存的回复")
                    return cached_response
            
            # 调用LLM
            response = await self._call_llm_with_retry(prompt, config)
            
            if response:
                # 清理和验证响应
                cleaned_response = self.response_validator.clean_response(response)
                quality = self.response_validator.validate_response(cleaned_response, context)
                
                # 更新质量统计
                self.stats["quality_distribution"][quality.value] += 1
                
                # 如果质量太差，使用回退消息
                if quality == ResponseQuality.POOR:
                    self.logger.warning(f"生成的回复质量较差: {cleaned_response[:50]}...")
                    return fallback_message or "抱歉，我需要重新组织一下语言。"
                
                # 缓存高质量回复
                if config.enable_cache and quality in [ResponseQuality.EXCELLENT, ResponseQuality.GOOD]:
                    cache_key = self._generate_cache_key(prompt, config)
                    self._save_to_cache(cache_key, cleaned_response, config.cache_ttl)
                
                self.stats["successful_calls"] += 1
                self.logger.info(f"成功生成动态回复，质量: {quality.value}")
                return cleaned_response
            else:
                self.logger.warning("LLM返回空响应")
                return fallback_message or "抱歉，暂时无法生成回复。"
                
        except Exception as e:
            self.logger.error(f"生成动态回复失败: {e}", exc_info=True)
            self.stats["failed_calls"] += 1
            return fallback_message or "抱歉，生成回复时出现错误。"
        
        finally:
            # 更新响应时间统计
            response_time = time.time() - start_time
            self._update_response_time_stats(response_time)
    
    async def _call_llm_with_retry(self, prompt: str, config: LLMCallConfig) -> Optional[str]:
        """带重试的LLM调用"""
        last_exception = None
        
        for attempt in range(config.max_retries + 1):
            try:
                # 构建消息
                messages = [{"role": "user", "content": prompt}]
                
                # 调用LLM
                response = await asyncio.wait_for(
                    self.llm_client.call_llm(
                        messages=messages,
                        agent_name=config.agent_name,
                        temperature=config.temperature,
                        max_tokens=config.max_tokens
                    ),
                    timeout=config.timeout
                )
                
                content = response.get("content", "").strip()
                if content:
                    return content
                else:
                    self.logger.warning(f"LLM返回空内容，尝试 {attempt + 1}/{config.max_retries + 1}")
                    
            except asyncio.TimeoutError:
                last_exception = "LLM调用超时"
                self.logger.warning(f"LLM调用超时，尝试 {attempt + 1}/{config.max_retries + 1}")
            except Exception as e:
                last_exception = str(e)
                self.logger.warning(f"LLM调用失败: {e}，尝试 {attempt + 1}/{config.max_retries + 1}")
            
            # 如果不是最后一次尝试，等待一下再重试
            if attempt < config.max_retries:
                await asyncio.sleep(0.5 * (attempt + 1))  # 递增等待时间
        
        self.logger.error(f"LLM调用最终失败，已重试 {config.max_retries} 次，最后错误: {last_exception}")
        return None
    
    def _generate_cache_key(self, prompt: str, config: LLMCallConfig) -> str:
        """生成缓存键"""
        import hashlib
        key_data = f"{prompt}_{config.agent_name}_{config.temperature}_{config.max_tokens}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[str]:
        """从缓存获取"""
        if cache_key in self._cache:
            cached_data = self._cache[cache_key]
            if time.time() < cached_data["expires_at"]:
                return cached_data["response"]
            else:
                # 缓存过期，删除
                del self._cache[cache_key]
        return None
    
    def _save_to_cache(self, cache_key: str, response: str, ttl: int):
        """保存到缓存"""
        self._cache[cache_key] = {
            "response": response,
            "expires_at": time.time() + ttl
        }
        
        # 简单的缓存清理（保持缓存大小在合理范围内）
        if len(self._cache) > 1000:
            # 删除最旧的一半缓存项
            sorted_items = sorted(self._cache.items(), key=lambda x: x[1]["expires_at"])
            for key, _ in sorted_items[:500]:
                del self._cache[key]
    
    def _update_response_time_stats(self, response_time: float):
        """更新响应时间统计"""
        current_avg = self.stats["average_response_time"]
        total_calls = self.stats["total_calls"]
        
        # 计算新的平均响应时间
        self.stats["average_response_time"] = (
            (current_avg * (total_calls - 1) + response_time) / total_calls
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        total_calls = self.stats["total_calls"]
        return {
            **self.stats,
            "success_rate": self.stats["successful_calls"] / max(total_calls, 1),
            "failure_rate": self.stats["failed_calls"] / max(total_calls, 1),
            "cache_hit_rate": self.stats["cache_hits"] / max(total_calls, 1),
            "cache_size": len(self._cache)
        }
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self.logger.info("已清空动态回复生成器缓存")
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "cache_hits": 0,
            "average_response_time": 0.0,
            "quality_distribution": {
                "excellent": 0,
                "good": 0,
                "acceptable": 0,
                "poor": 0
            }
        }
        self.logger.info("已重置动态回复生成器统计信息")


class DynamicReplyFactory:
    """动态回复生成工厂类 - 提供便捷的预配置生成方法"""

    def __init__(self, generator: DynamicReplyGenerator):
        self.generator = generator
        self.logger = logging.getLogger(__name__)

    async def generate_greeting_reply(
        self,
        prompt_instruction: str,
        user_message: str = "",
        session_id: str = ""
    ) -> str:
        """生成问候回复"""
        # 添加调试日志
        self.logger.info(f"[DEBUG] generate_greeting_reply 接收到的 prompt_instruction: {prompt_instruction}")

        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id
        )

        config = LLMCallConfig(
            agent_name="greeting_generator",
            temperature=0.7,
            max_tokens=150
        )

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.SIMPLE,
            fallback_message="您好！很高兴为您服务。请问有什么可以帮您？"
        )

    async def generate_empathy_reply(
        self,
        prompt_instruction: str,
        user_message: str,
        session_id: str = ""
    ) -> str:
        """生成共情回复"""
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id
        )

        config = LLMCallConfig(
            agent_name="empathy_generator",
            temperature=0.7,
            max_tokens=200
        )

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.WITH_USER_INPUT,
            fallback_message="我理解您的感受。请问有什么我可以帮助您的吗？"
        )

    async def generate_clarification_reply(
        self,
        prompt_instruction: str,
        user_message: str,
        session_id: str = ""
    ) -> str:
        """生成澄清回复"""
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id
        )

        config = LLMCallConfig(
            agent_name="clarification_generator",
            temperature=0.7,
            max_tokens=200
        )

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.WITH_USER_INPUT,
            fallback_message="抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"
        )

    async def generate_apology_reply(
        self,
        prompt_instruction: str,
        user_message: str,
        session_id: str = ""
    ) -> str:
        """生成道歉回复"""
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_message,
            session_id=session_id
        )

        config = LLMCallConfig(
            agent_name="apology_generator",
            temperature=0.7,
            max_tokens=200
        )

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.WITH_USER_INPUT,
            fallback_message="非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？"
        )

    async def generate_question_polish(
        self,
        user_input: str,
        base_question: str,
        session_id: str = ""
    ) -> str:
        """生成润色后的问题"""
        context = GenerationContext(
            prompt_instruction="",  # 将通过模板设置
            user_message=user_input,
            session_id=session_id,
            template_name="question_polisher",
            template_variables={
                "user_input": user_input,
                "base_question": base_question
            }
        )

        config = LLMCallConfig(
            agent_name="question_polisher",
            temperature=0.7,
            max_tokens=300
        )

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.TEMPLATE_BASED,
            fallback_message=base_question
        )

    async def generate_clarification_question(
        self,
        focus_point_name: str,
        focus_point_description: str,
        user_answer: str,
        conversation_history: str = "",
        session_id: str = ""
    ) -> str:
        """生成澄清问题"""
        context = GenerationContext(
            prompt_instruction="",  # 将通过模板设置
            user_message=user_answer,
            session_id=session_id,
            template_name="clarification_question",
            template_variables={
                "focus_point_name": focus_point_name,
                "focus_point_description": focus_point_description,
                "conversation_history": conversation_history,
                "user_answer": user_answer
            }
        )

        config = LLMCallConfig(
            agent_name="clarification_generator",
            temperature=0.7,
            max_tokens=250
        )

        return await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.TEMPLATE_BASED,
            fallback_message=f"关于「{focus_point_name}」，您能提供更详细一些的信息吗？"
        )

    async def generate_domain_guidance(
        self,
        prompt_instruction: str,
        user_input: str,
        domains_text: str = "",
        session_id: str = ""
    ) -> str:
        """
        生成领域引导问题

        Args:
            prompt_instruction: 决策引擎提供的指令
            user_input: 用户输入
            domains_text: 格式化的领域信息文本
            session_id: 会话ID

        Returns:
            str: 生成的领域引导问题
        """
        context = GenerationContext(
            prompt_instruction=prompt_instruction,
            user_message=user_input,
            session_id=session_id,
            template_name="domain_guidance",  # 使用domain_guidance模板
            template_variables={
                "user_input": user_input,
                "domains_text": domains_text,
                "prompt_instruction": prompt_instruction
            }
        )

        config = LLMCallConfig(
            agent_name="domain_guidance_generator",
            temperature=0.7,
            max_tokens=300
        )

        # 自定义响应验证函数
        def custom_validator(response: str) -> bool:
            """验证领域引导问题的质量"""
            if not response or len(response) < 10:
                return False
            # 检查是否包含问号
            return "?" in response or "？" in response

        response = await self.generator.generate_reply(
            context=context,
            config=config,
            strategy=PromptStrategy.TEMPLATE_BASED,  # 改为使用模板策略
            fallback_message="请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"
        )

        # 额外的质量检查
        if custom_validator(response):
            return response
        else:
            self.logger.warning(f"生成的领域引导问题质量不佳: {response}")
            return "请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"
