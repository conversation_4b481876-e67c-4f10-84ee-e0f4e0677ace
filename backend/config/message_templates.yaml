# 消息模板配置文件
# 用于管理系统中所有的消息模板，支持占位符替换

# 文档相关消息模板
document:
  # 文档修改提示词
  modification:
    prompt_instruction: "用户要求修改文档，请先清晰地复述一遍你理解的修改点以进行确认，然后说明将如何执行修改。"
    
  # 文档确认前缀
  confirmation:
    prefix: "根据您提供的信息，我已生成需求文档。请查看并确认：\n\n"
    
  # 文档操作指引
  guidance:
    header: "\n\n---\n文档操作指引：\n"
    template: |
      文档操作指引：
      1. 输入'确认' - 确认文档无误并完成
      2. 指出需要修改的部分 - 例如'修改功能描述部分'
      3. 输入'新需求' - 开始新的需求采集
      4. 输入'全部重来' - 重新开始整个需求采集流程
    
  # 文档生成失败消息
  generation_failed: "抱歉，文档生成失败，请稍后重试。"
  
  # 文档未找到消息
  not_found: "抱歉，未找到相关文档。"

# 错误和异常消息模板
error:
  # LLM相关错误
  llm:
    not_initialized: "LLM client未初始化，无法执行{context}"
    call_failed: "LLM调用失败: {error}"
    timeout: "LLM调用超时，请稍后重试"
    
  # 数据库相关错误
  database:
    connection_failed: "数据库连接失败: {error}"
    query_failed: "数据库查询失败: {error}"
    update_failed: "数据库更新失败: {error}"
    
  # 通用错误消息
  general:
    unknown_error: "发生未知错误，请稍后重试"
    invalid_input: "输入格式不正确，请检查后重试"
    permission_denied: "权限不足，无法执行此操作"

# 澄清和请求消息模板
clarification:
  # 通用澄清请求
  general:
    request: "抱歉，我没有完全理解您的意思，能否请您用其他方式重新描述一下？"
    polite: "为了更好地帮助您，能否请您提供更多详细信息？"
    
  # 特定场景澄清
  specific:
    focus_point: "关于"{focus_point_name}"，您能提供更详细一些的信息吗？"
    requirement: "关于您的需求，我需要了解更多细节，请问您能详细说明一下吗？"
    
  # 追问模板
  follow_up:
    incomplete_info: "您提供的信息很有帮助，不过关于{topic}，能否再详细说明一下？"
    need_examples: "能否举个具体的例子来说明{topic}？"

# 共情和情感回应模板
empathy:
  # 负面情绪回应
  negative:
    general: "听到这个消息我感到很难过。请问有什么我可以帮助您的吗？"
    frustrated: "我理解您的困扰，让我们一起来解决这个问题。"
    confused: "我明白这可能让您感到困惑，让我来帮您理清思路。"
    
  # 积极情绪回应
  positive:
    excited: "我能感受到您的热情！让我们把这个想法变成现实。"
    satisfied: "很高兴您对结果满意！还有什么其他需要帮助的吗？"

# 问候和欢迎消息模板
greeting:
  # 基础问候
  basic:
    welcome: "您好！我是AI需求采集助手，专门帮助您整理和分析业务需求。请问有什么需求需要帮助整理？"
    return_user: "欢迎回来！请问这次有什么新的需求需要我帮助您整理吗？"
    
  # 个性化问候
  personalized:
    template: "您好{user_name}！我是AI需求采集助手，专门帮助您整理和分析业务需求。请问有什么需求需要帮助整理？"
    
  # 时间相关问候
  time_based:
    morning: "早上好！准备开始新的需求采集工作了吗？"
    afternoon: "下午好！有什么需求需要我帮您整理吗？"
    evening: "晚上好！今天有什么需求想要讨论吗？"

# 引导和建议消息模板
guidance:
  # 领域引导
  domain:
    template: "根据您的描述，这可能涉及以下几个领域：\n{domains_text}\n\n请问您的需求主要属于哪个领域？或者您可以提供更多细节帮助我更好地理解。"
    fallback: "为了更好地帮助您，请问您的需求主要涉及哪个业务领域？"
    
  # 建议提供
  suggestions:
    single_point: "关于"{point_name}"，我的一点建议是：\n\n{suggestions}\n\n您对此有什么看法？"
    multiple_points: "关于"{point_name}"，我这里有一些建议供您参考：\n\n{formatted_suggestions}\n\n您觉得哪几点比较符合您的想法，或者您有其他补充吗？"
    general: "基于您目前提供的全部信息，我有以下几点通用建议：\n\n{formatted_suggestions}\n\n您希望我详细解释哪个方面呢？"

# 状态转换消息模板
state_transition:
  # 重置确认
  reset:
    confirmation: "好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？"
    
  # 状态切换通知
  transition:
    to_collecting: "现在开始收集您的需求信息。"
    to_documenting: "信息收集完成，正在生成需求文档。"
    to_idle: "已返回初始状态，随时可以开始新的对话。"

# 问题生成模板
question:
  # 基础问题模板
  base:
    template: "关于'{focus_point_name}'，{description}"
    with_example: "关于'{focus_point_name}'，{description} {example}"
    
  # 润色后的问题模板
  polished:
    prefix: "让我们来聊聊"
    suffix: "，您有什么想法吗？"
    
  # 跳过问题的回应
  skip:
    acknowledgment: "好的，我们跳过这个问题，继续下一个。"
    next_question: "好的，我们继续下一个问题。"

# 完成和确认消息模板
completion:
  # 任务完成
  task:
    success: "太好了！我们已经完成了需求收集。"
    partial: "我们已经收集了大部分信息，还有一些细节需要确认。"

  # 文档完成
  document:
    generated: "需求文档已生成完成。"
    confirmed: "用户确认文档，完成需求采集流程。"
    
  # 会话结束
  session:
    end: "本次需求采集已完成，感谢您的配合！"
    continue: "还有其他需求需要讨论吗？"

# 系统状态消息模板
system:
  # 处理中状态
  processing:
    analyzing: "正在分析您的需求..."
    generating: "正在生成回复..."
    extracting: "正在提取信息..."
    
  # 等待状态
  waiting:
    user_input: "请输入您的回复..."
    confirmation: "请确认是否继续..."
    
  # 错误恢复
  recovery:
    retry: "正在重试..."
    fallback: "使用备用方案处理..."

# 帮助和说明消息模板
help:
  # 使用说明
  usage:
    basic: "您可以直接描述您的需求，我会帮您整理和分析。"
    commands: "您可以使用以下指令：'重置' - 重新开始，'跳过' - 跳过当前问题，'帮助' - 查看使用说明。"
    
  # 功能介绍
  features:
    overview: "我可以帮您：1. 收集和整理需求信息 2. 生成结构化的需求文档 3. 提供专业的建议和指导"

# 多语言支持模板（预留）
i18n:
  # 语言切换
  language:
    switch_success: "语言已切换为{language}"
    switch_failed: "语言切换失败，继续使用当前语言"
    not_supported: "暂不支持{language}语言"
