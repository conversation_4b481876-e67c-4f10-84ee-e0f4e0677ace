# 业务规则配置文件
# 用于配置系统的核心业务逻辑参数

# 完整度判断规则
completeness:
  # 信息完整度阈值，超过此值认为信息收集完成
  threshold: 0.7
  # 状态映射规则
  status_mapping:
    completed: 0.7    # 完成状态的最低完整度
    pending: 0.0      # 待处理状态的完整度范围

# 重试机制配置
retry:
  # 最大未完成尝试次数
  max_pending_attempts: 3
  # 最大重试次数
  max_retry_attempts: 3
  # 重试延迟时间（秒）
  retry_delay: 1

# 优先级配置
priority:
  # 文档修改操作的优先级
  document_modification: 7
  # 完成并重置操作的优先级
  finalize_and_reset: 8
  # 默认优先级
  default: 1
  # 问候回复的优先级
  greeting: 1

# 文档操作指引配置
document_guidance:
  # 是否启用文档操作指引
  enabled: true
  # 指引选项配置
  options:
    - key: "confirm"
      text: "输入'确认' - 确认文档无误并完成"
      keywords: ["确认", "ok", "okay", "confirm", "yes", "good", "fine"]
    - key: "modify"
      text: "指出需要修改的部分 - 例如'修改功能描述部分'"
      keywords: ["修改", "调整", "改", "change", "modify"]
    - key: "new_requirement"
      text: "输入'新需求' - 开始新的需求采集"
      keywords: ["新需求", "新的需求", "new requirement"]
    - key: "restart"
      text: "输入'全部重来' - 重新开始整个需求采集流程"
      keywords: ["全部重来", "重新开始", "restart", "重来"]

# 文档确认判断规则
document_confirmation:
  # 确认关键词
  confirmation_keywords:
    - "确认"
    - "ok"
    - "okay" 
    - "confirm"
    - "yes"
    - "good"
    - "fine"
    - "approve"
    - "accept"
  
  # 否定和修改关键词
  negation_keywords:
    - "不"
    - "不是"
    - "不要"
    - "别"
    - "修改"
    - "调整"
    - "但是"
    - "不过"
    - "no"
    - "not"
    - "modify"
    - "change"
    - "but"

# 问题生成规则
question_generation:
  # 是否启用问题润色
  enable_polishing: true
  # 润色失败时的回退策略
  fallback_strategy: "use_base_question"
  # 历史对话上下文长度限制
  max_history_turns: 5

# 状态转换规则
state_transition:
  # 自动状态转换的条件
  auto_transition:
    # 从COLLECTING_INFO到DOCUMENTING的条件
    to_documenting:
      # 所有必需关注点都已完成
      all_required_completed: true
      # 最低完成的关注点数量
      min_completed_points: 3
  
  # 状态恢复规则
  state_recovery:
    # 默认状态
    default_state: "IDLE"
    # 恢复失败时的安全状态
    safe_state: "IDLE"

# 信息提取规则
information_extraction:
  # 是否启用批量提取
  enable_batch_extraction: true
  # 单次提取的最大关注点数量
  max_points_per_batch: 5
  # 提取失败时的重试次数
  extraction_retry_count: 2

# 会话管理规则
session_management:
  # 会话超时时间（秒）
  session_timeout: 3600
  # 自动清理间隔（秒）
  cleanup_interval: 300
  # 最大并发会话数
  max_concurrent_sessions: 100

# 性能优化规则
performance:
  # 是否启用缓存
  enable_caching: true
  # 缓存TTL（秒）
  cache_ttl: 3600
  # 最大缓存大小
  max_cache_size: 1000
  
  # 数据库查询优化
  database:
    # 是否启用查询缓存
    enable_query_cache: true
    # 批量操作的最大大小
    max_batch_size: 100
    # 连接池大小
    connection_pool_size: 10

# 日志记录规则
logging:
  # 详细日志级别的操作
  debug_operations:
    - "focus_point_status_update"
    - "state_transition"
    - "llm_call"
  
  # 需要记录性能指标的操作
  performance_tracking:
    - "document_generation"
    - "information_extraction"
    - "llm_response_time"
  
  # 敏感信息脱敏规则
  sensitive_data_masking:
    enabled: true
    mask_patterns:
      - "api_key"
      - "password"
      - "token"

# 错误处理规则
error_handling:
  # 自动重试的错误类型
  auto_retry_errors:
    - "timeout"
    - "connection_error"
    - "rate_limit"
  
  # 需要立即失败的错误类型
  immediate_fail_errors:
    - "authentication_error"
    - "permission_denied"
  
  # 错误恢复策略
  recovery_strategies:
    llm_error: "use_fallback_response"
    database_error: "retry_with_backoff"
    network_error: "retry_with_exponential_backoff"

# 多语言支持规则
internationalization:
  # 默认语言
  default_language: "zh-CN"
  # 支持的语言列表
  supported_languages:
    - "zh-CN"
    - "en-US"
  # 自动语言检测
  auto_detect_language: false

# 安全规则
security:
  # 输入验证规则
  input_validation:
    # 最大输入长度
    max_input_length: 10000
    # 禁止的字符模式
    forbidden_patterns:
      - "<script"
      - "javascript:"
      - "data:text/html"
  
  # 输出过滤规则
  output_filtering:
    # 是否启用HTML转义
    enable_html_escape: true
    # 是否过滤敏感信息
    filter_sensitive_info: true
